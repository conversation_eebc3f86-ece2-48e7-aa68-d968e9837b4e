"""
简化的鉴别器实现，避免复杂的PCF计算
"""

import torch
import torch.nn as nn


class SimpleDiscriminator(nn.Module):
    """简化的鉴别器，使用标准的神经网络而不是PCF"""
    
    def __init__(self, input_size, hidden_size=64):
        super(SimpleDiscriminator, self).__init__()
        self.input_size = input_size
        
        self.net = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.LeakyReLU(0.2),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.LeakyReLU(0.2),
            nn.Linear(hidden_size // 2, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        """
        Args:
            x: (batch_size, seq_len, features)
        Returns:
            score: (batch_size, 1)
        """
        # 将时间序列展平
        batch_size = x.shape[0]
        x_flat = x.view(batch_size, -1)
        return self.net(x_flat)
    
    def distance_measure(self, x1, x2):
        """
        计算两个时间序列批次之间的距离
        使用鉴别器输出的差异作为距离度量
        """
        score1 = self.forward(x1)
        score2 = self.forward(x2)
        
        # 使用L2距离
        distance = torch.mean((score1 - score2) ** 2)
        return distance


class MemoryEfficientDiscriminator(nn.Module):
    """内存高效的鉴别器，使用卷积层处理时间序列"""
    
    def __init__(self, input_size, hidden_size=32):
        super(MemoryEfficientDiscriminator, self).__init__()
        self.input_size = input_size
        
        # 使用1D卷积处理时间序列
        self.conv_layers = nn.Sequential(
            nn.Conv1d(input_size, hidden_size, kernel_size=3, padding=1),
            nn.LeakyReLU(0.2),
            nn.Conv1d(hidden_size, hidden_size // 2, kernel_size=3, padding=1),
            nn.LeakyReLU(0.2),
            nn.AdaptiveAvgPool1d(1),  # 全局平均池化
        )
        
        self.classifier = nn.Sequential(
            nn.Linear(hidden_size // 2, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        """
        Args:
            x: (batch_size, seq_len, features)
        Returns:
            score: (batch_size, 1)
        """
        # 转换为卷积格式: (batch_size, features, seq_len)
        x = x.permute(0, 2, 1)
        
        # 卷积特征提取
        features = self.conv_layers(x)  # (batch_size, hidden_size//2, 1)
        features = features.squeeze(-1)  # (batch_size, hidden_size//2)
        
        # 分类
        score = self.classifier(features)
        return score
    
    def distance_measure(self, x1, x2):
        """计算距离度量"""
        score1 = self.forward(x1)
        score2 = self.forward(x2)
        
        # 使用Wasserstein距离的近似
        distance = torch.mean(score1) - torch.mean(score2)
        return distance


class MSEDiscriminator(nn.Module):
    """最简单的鉴别器，直接使用MSE损失"""
    
    def __init__(self, input_size):
        super(MSEDiscriminator, self).__init__()
        self.input_size = input_size
    
    def distance_measure(self, x1, x2):
        """直接使用MSE作为距离度量"""
        # 确保形状匹配
        min_batch = min(x1.shape[0], x2.shape[0])
        x1_small = x1[:min_batch]
        x2_small = x2[:min_batch]
        
        return nn.MSELoss()(x1_small, x2_small)


def create_memory_efficient_discriminator(input_size, discriminator_type="simple"):
    """创建内存高效的鉴别器"""
    if discriminator_type == "simple":
        return SimpleDiscriminator(input_size)
    elif discriminator_type == "conv":
        return MemoryEfficientDiscriminator(input_size)
    elif discriminator_type == "mse":
        return MSEDiscriminator(input_size)
    else:
        raise ValueError(f"Unknown discriminator type: {discriminator_type}")
