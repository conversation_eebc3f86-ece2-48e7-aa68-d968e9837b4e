"""
测试简化版本的GAN模型
"""

import torch
import numpy as np
from train_stage2 import Config, TimeMixerGAN
import os

def test_simplified_gan():
    """测试简化版本的GAN模型"""
    print("Testing simplified GAN model...")
    
    # 创建配置
    config = Config()
    config.update_dims(3)  # 3个特征
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # 清理GPU缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    try:
        # 创建模型
        print("Creating simplified GAN model...")
        gan_model = TimeMixerGAN(config).to(device)
        
        print("Model components:")
        print(f"  - Season discriminator type: {type(gan_model.season_discriminator).__name__}")
        print(f"  - Trend discriminator type: {type(gan_model.trend_discriminator).__name__}")
        print(f"  - Reconstruction discriminator type: {type(gan_model.reconstruction_discriminator).__name__}")
        
        # 创建测试数据
        print("Creating test data...")
        batch_size = 8
        test_data = torch.randn(batch_size, config.seq_len, config.enc_in).to(device)
        
        print("Testing encoder...")
        with torch.no_grad():
            season_list, trend_list, mean, stdev = gan_model.encoder(test_data)
            print(f"✓ Encoder works! Season list length: {len(season_list)}")
        
        print("Testing generators...")
        with torch.no_grad():
            fake_trend_list, fake_season_list = gan_model.generate_fake_sequences(batch_size, device)
            print(f"✓ Generators work! Generated {len(fake_trend_list)} scales")
        
        print("Testing simplified discriminators...")
        with torch.no_grad():
            # 测试季节鉴别器
            real_season = season_list[0]
            fake_season = fake_season_list[0]
            
            season_distance = gan_model.season_discriminator.distance_measure(real_season, fake_season)
            print(f"✓ Season discriminator works! Distance: {season_distance.item():.6f}")
            
            # 测试趋势鉴别器
            real_trend = trend_list[0]
            fake_trend = fake_trend_list[0]
            
            trend_distance = gan_model.trend_discriminator.distance_measure(real_trend, fake_trend)
            print(f"✓ Trend discriminator works! Distance: {trend_distance.item():.6f}")
            
            # 测试重构鉴别器
            fake_recon = gan_model.reconstruct_from_components(fake_trend_list, fake_season_list)
            gan_model.denormalizer.mean = mean
            gan_model.denormalizer.stdev = stdev
            fake_denorm = gan_model.denormalizer(fake_recon, 'denorm')
            
            recon_distance = gan_model.reconstruction_discriminator.distance_measure(test_data, fake_denorm)
            print(f"✓ Reconstruction discriminator works! Distance: {recon_distance.item():.6f}")
        
        print("Testing training step simulation...")
        # 模拟一个训练步骤
        gan_model.train()
        
        # 创建优化器
        g_optimizer = torch.optim.Adam(
            list(gan_model.trend_generator.parameters()) + list(gan_model.season_generator.parameters()),
            lr=0.001
        )
        
        d_optimizer = torch.optim.Adam(
            list(gan_model.season_discriminator.parameters()) + 
            list(gan_model.trend_discriminator.parameters()) + 
            list(gan_model.reconstruction_discriminator.parameters()),
            lr=0.001
        )
        
        # 鉴别器训练步骤
        d_optimizer.zero_grad()
        
        season_d_loss = -gan_model.season_discriminator.distance_measure(real_season, fake_season)
        trend_d_loss = -gan_model.trend_discriminator.distance_measure(real_trend, fake_trend)
        recon_d_loss = -gan_model.reconstruction_discriminator.distance_measure(test_data, fake_denorm)
        
        d_loss = season_d_loss + trend_d_loss + recon_d_loss
        d_loss.backward()
        d_optimizer.step()
        
        print(f"✓ Discriminator training step works! D_loss: {d_loss.item():.6f}")
        
        # 生成器训练步骤
        g_optimizer.zero_grad()
        
        # 重新生成（需要梯度）
        fake_trend_list, fake_season_list = gan_model.generate_fake_sequences(batch_size, device)
        fake_recon = gan_model.reconstruct_from_components(fake_trend_list, fake_season_list)
        fake_denorm = gan_model.denormalizer(fake_recon, 'denorm')
        
        season_g_loss = gan_model.season_discriminator.distance_measure(real_season, fake_season_list[0])
        trend_g_loss = gan_model.trend_discriminator.distance_measure(real_trend, fake_trend_list[0])
        recon_g_loss = gan_model.reconstruction_discriminator.distance_measure(test_data, fake_denorm)
        
        g_loss = season_g_loss + trend_g_loss + recon_g_loss
        g_loss.backward()
        g_optimizer.step()
        
        print(f"✓ Generator training step works! G_loss: {g_loss.item():.6f}")
        
        print("✓ All simplified GAN tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

if __name__ == "__main__":
    print("=" * 60)
    print("Simplified TimeMixer GAN Tests")
    print("=" * 60)
    
    success = test_simplified_gan()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Simplified GAN model works perfectly!")
        print("The model now uses memory-efficient discriminators.")
        print("\nKey improvements:")
        print("- Replaced PCF discriminators with simple neural networks")
        print("- Reduced memory usage significantly")
        print("- Maintained the same training interface")
        print("- Should work on GPUs with limited memory")
    else:
        print("❌ Test failed. Please check the implementation.")
    print("=" * 60)
