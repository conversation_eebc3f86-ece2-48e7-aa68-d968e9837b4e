"""
测试数据加载器修复
"""

import torch
import numpy as np
from train_stage2 import Config, TimeMixerGAN, test_gan_model
from data_loader import load_data
import os

def test_dataloader_compatibility():
    """测试数据加载器兼容性"""
    print("=" * 60)
    print("测试数据加载器兼容性")
    print("=" * 60)
    
    # 创建配置
    config = Config()
    
    try:
        # 加载真实数据
        print("1. 加载真实数据...")
        train_loader, num_features = load_data(config.file_path, config.seq_len, config.batch_size)
        test_loader, _ = load_data(config.file_path, config.seq_len, config.batch_size)
        print(f"✅ 数据加载成功")
        print(f"   特征数量: {num_features}")
        print(f"   训练集批次数: {len(train_loader)}")
        print(f"   测试集批次数: {len(test_loader)}")
        
        # 检查数据格式
        print("\n2. 检查数据格式...")
        for i, batch_data in enumerate(test_loader):
            print(f"   批次 {i} 数据类型: {type(batch_data)}")
            
            if isinstance(batch_data, (list, tuple)):
                print(f"   批次 {i} 是序列，长度: {len(batch_data)}")
                for j, item in enumerate(batch_data):
                    if isinstance(item, torch.Tensor):
                        print(f"     项目 {j}: Tensor, 形状: {item.shape}")
                    else:
                        print(f"     项目 {j}: {type(item)}")
                batch_x = batch_data[0]
            else:
                print(f"   批次 {i} 是单个项目")
                if isinstance(batch_data, torch.Tensor):
                    print(f"     Tensor, 形状: {batch_data.shape}")
                batch_x = batch_data
            
            print(f"   最终使用的数据形状: {batch_x.shape}")
            
            if i >= 2:  # 只检查前3个批次
                break
        
        # 测试GAN模型
        print("\n3. 测试GAN模型...")
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 更新配置维度
        config.update_dims(num_features)
        print(f"   数据特征维度: {num_features}")
        
        # 创建GAN模型
        gan_model = TimeMixerGAN(config).to(device)
        
        # 创建输出目录
        os.makedirs('test_outputs', exist_ok=True)
        
        # 测试GAN测试函数
        print("\n4. 运行GAN测试函数...")
        try:
            test_gan_model(gan_model, test_loader, device, epoch=50, outputs_dir='test_outputs')
            print("✅ GAN测试函数运行成功！")
            
            # 检查生成的文件
            expected_files = [
                'test_outputs/gan_pca_epoch_50.png',
                'test_outputs/gan_tsne_epoch_50.png'
            ]
            
            print("\n5. 检查生成的文件...")
            for file_path in expected_files:
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path) / 1024
                    print(f"✅ {file_path} 已生成 ({file_size:.1f} KB)")
                else:
                    print(f"❌ {file_path} 未找到")
            
        except Exception as e:
            print(f"❌ GAN测试函数失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("\n" + "=" * 60)
        print("🎉 数据加载器兼容性测试完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_dataloader_formats():
    """测试不同的数据加载器格式"""
    print("\n" + "=" * 60)
    print("测试不同数据加载器格式的兼容性")
    print("=" * 60)
    
    # 模拟不同格式的数据加载器
    class TupleDataLoader:
        def __init__(self):
            self.data = [
                (torch.randn(4, 24, 3),),  # 元组格式
                (torch.randn(4, 24, 3),),
            ]
        def __iter__(self):
            return iter(self.data)
        def __len__(self):
            return len(self.data)
    
    class ListDataLoader:
        def __init__(self):
            self.data = [
                [torch.randn(4, 24, 3)],  # 列表格式
                [torch.randn(4, 24, 3)],
            ]
        def __iter__(self):
            return iter(self.data)
        def __len__(self):
            return len(self.data)
    
    class TensorDataLoader:
        def __init__(self):
            self.data = [
                torch.randn(4, 24, 3),  # 直接Tensor
                torch.randn(4, 24, 3),
            ]
        def __iter__(self):
            return iter(self.data)
        def __len__(self):
            return len(self.data)
    
    loaders = {
        "元组格式": TupleDataLoader(),
        "列表格式": ListDataLoader(), 
        "直接Tensor": TensorDataLoader()
    }
    
    for name, loader in loaders.items():
        print(f"\n测试 {name}:")
        try:
            for i, batch_data in enumerate(loader):
                # 使用修复后的逻辑
                if isinstance(batch_data, (list, tuple)):
                    batch_x = batch_data[0]
                else:
                    batch_x = batch_data
                
                print(f"  ✅ {name} - 批次 {i}: {batch_x.shape}")
                
                if i >= 1:  # 只测试前2个批次
                    break
        except Exception as e:
            print(f"  ❌ {name} 失败: {e}")

if __name__ == "__main__":
    success = test_dataloader_compatibility()
    test_different_dataloader_formats()
    
    if success:
        print("\n🎯 所有测试通过！数据加载器兼容性问题已解决。")
        print("现在可以安全运行完整的GAN训练了。")
    else:
        print("\n⚠ 部分测试失败，请检查数据加载器实现。")
