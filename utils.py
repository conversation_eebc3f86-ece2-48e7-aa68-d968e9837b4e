import torch
import numpy as np


def get_time_vector(size: int, length: int) -> torch.Tensor:
    return torch.linspace(1/length, 1, length).reshape(1, -1, 1).repeat(size, 1, 1)


def AddTime(x):
    """Add time dimension to input tensor"""
    t = get_time_vector(x.shape[0], x.shape[1]).to(x.device)
    return torch.cat([t, x], dim=-1)


def to_numpy(x):
    """
    Casts torch.Tensor to a numpy ndarray.

    The function detaches the tensor from its gradients, then puts it onto the cpu and at last casts it to numpy.
    """
    return x.detach().cpu().numpy()


def toggle_grad(model, requires_grad):
    """Toggle gradient computation for model parameters"""
    for p in model.parameters():
        p.requires_grad_(requires_grad)
