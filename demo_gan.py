"""
TimeMixer GAN 演示脚本
展示如何使用新构建的GAN模型进行时间序列生成
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from train_stage2 import Config, TimeMixerGAN
import os

def create_synthetic_data(num_samples=1000, seq_len=24, num_features=3):
    """创建合成时间序列数据用于演示"""
    print(f"Creating synthetic data: {num_samples} samples, {seq_len} timesteps, {num_features} features")
    
    # 创建具有趋势和季节性的合成数据
    data = []
    for i in range(num_samples):
        # 基础趋势
        trend = np.linspace(0, 1, seq_len) * np.random.normal(1, 0.2)
        
        # 季节性成分
        seasonal = np.sin(2 * np.pi * np.arange(seq_len) / 12) * np.random.normal(0.5, 0.1)
        
        # 噪声
        noise = np.random.normal(0, 0.1, seq_len)
        
        # 组合多个特征
        sample = []
        for f in range(num_features):
            feature = trend + seasonal * (f + 1) + noise
            sample.append(feature)
        
        data.append(np.array(sample).T)  # (seq_len, num_features)
    
    return np.array(data)  # (num_samples, seq_len, num_features)

def demo_gan_training():
    """演示GAN训练过程"""
    print("=" * 60)
    print("TimeMixer GAN 演示")
    print("=" * 60)
    
    # 配置
    config = Config()
    num_features = 3
    config.update_dims(num_features)
    config.stage1_epochs = 5  # 减少epoch用于演示
    config.stage2_epochs = 10
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 创建合成数据
    synthetic_data = create_synthetic_data(
        num_samples=200, 
        seq_len=config.seq_len, 
        num_features=num_features
    )
    
    # 转换为PyTorch张量
    data_tensor = torch.FloatTensor(synthetic_data)
    
    # 创建简单的数据加载器
    class SimpleDataLoader:
        def __init__(self, data, batch_size):
            self.data = data
            self.batch_size = batch_size
            self.num_batches = len(data) // batch_size
        
        def __iter__(self):
            indices = torch.randperm(len(self.data))
            for i in range(0, len(indices), self.batch_size):
                batch_indices = indices[i:i+self.batch_size]
                yield (self.data[batch_indices],)
        
        def __len__(self):
            return self.num_batches
    
    train_loader = SimpleDataLoader(data_tensor, config.batch_size)
    test_loader = SimpleDataLoader(data_tensor[:50], 50)  # 小测试集
    
    print(f"数据形状: {synthetic_data.shape}")
    print(f"训练批次数: {len(train_loader)}")
    
    # 创建输出目录
    os.makedirs('demo_outputs', exist_ok=True)
    
    # 可视化原始数据
    plt.figure(figsize=(15, 5))
    for i in range(3):  # 显示前3个样本
        plt.subplot(1, 3, i+1)
        for f in range(num_features):
            plt.plot(synthetic_data[i, :, f], label=f'Feature {f+1}')
        plt.title(f'Sample {i+1}')
        plt.legend()
        plt.grid(True)
    plt.tight_layout()
    plt.savefig('demo_outputs/original_data_samples.png')
    plt.close()
    print("原始数据样本保存到: demo_outputs/original_data_samples.png")
    
    # 创建和训练模型
    print("\n--- 创建TimeMixer GAN模型 ---")
    gan_model = TimeMixerGAN(config).to(device)
    
    print("模型组件:")
    print(f"  - 编码器参数数量: {sum(p.numel() for p in gan_model.encoder.parameters())}")
    print(f"  - 解码器参数数量: {sum(p.numel() for p in gan_model.decoder.parameters())}")
    print(f"  - 趋势生成器参数数量: {sum(p.numel() for p in gan_model.trend_generator.parameters())}")
    print(f"  - 季节生成器参数数量: {sum(p.numel() for p in gan_model.season_generator.parameters())}")
    print(f"  - 季节鉴别器参数数量: {sum(p.numel() for p in gan_model.season_discriminator.parameters())}")
    print(f"  - 趋势鉴别器参数数量: {sum(p.numel() for p in gan_model.trend_discriminator.parameters())}")
    print(f"  - 重构鉴别器参数数量: {sum(p.numel() for p in gan_model.reconstruction_discriminator.parameters())}")
    
    # 演示生成过程
    print("\n--- 演示生成过程 ---")
    gan_model.eval()
    with torch.no_grad():
        # 生成一些样本
        fake_trend_list, fake_season_list = gan_model.generate_fake_sequences(5, device)
        print(f"生成的趋势列表长度: {len(fake_trend_list)}")
        print(f"生成的季节列表长度: {len(fake_season_list)}")
        print(f"第一个趋势形状: {fake_trend_list[0].shape}")
        print(f"第一个季节形状: {fake_season_list[0].shape}")

        # 重构时间序列
        fake_recon = gan_model.reconstruct_from_components(fake_trend_list, fake_season_list)
        print(f"重构序列形状: {fake_recon.shape}")
        
        # 可视化生成的组件
        plt.figure(figsize=(15, 10))
        
        # 趋势组件
        plt.subplot(2, 2, 1)
        fake_trends = fake_trend_list[0]  # 使用第一个尺度
        for i in range(min(3, fake_trends.shape[0])):
            plt.plot(fake_trends[i, :, 0].cpu().numpy(), label=f'Sample {i+1}')
        plt.title('Generated Trend Components (Feature 1)')
        plt.legend()
        plt.grid(True)

        # 季节组件
        plt.subplot(2, 2, 2)
        fake_seasons = fake_season_list[0]  # 使用第一个尺度
        for i in range(min(3, fake_seasons.shape[0])):
            plt.plot(fake_seasons[i, :, 0].cpu().numpy(), label=f'Sample {i+1}')
        plt.title('Generated Seasonal Components (Feature 1)')
        plt.legend()
        plt.grid(True)
        
        # 重构序列（需要反标准化，这里简化处理）
        plt.subplot(2, 2, 3)
        for i in range(min(3, fake_recon.shape[0])):
            plt.plot(fake_recon[i, :, 0].cpu().numpy(), label=f'Sample {i+1}')
        plt.title('Reconstructed Sequences (Feature 1)')
        plt.legend()
        plt.grid(True)
        
        # 对比原始数据
        plt.subplot(2, 2, 4)
        for i in range(min(3, len(synthetic_data))):
            plt.plot(synthetic_data[i, :, 0], label=f'Real Sample {i+1}')
        plt.title('Original Data (Feature 1)')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('demo_outputs/generated_components.png')
        plt.close()
        print("生成的组件保存到: demo_outputs/generated_components.png")
    
    print("\n--- 演示完成 ---")
    print("TimeMixer GAN模型已成功构建并可以运行!")
    print("主要特点:")
    print("  ✓ 集成了预训练的编码器和解码器")
    print("  ✓ 包含独立的趋势和季节生成器")
    print("  ✓ 实现了三个专门的鉴别器")
    print("  ✓ 支持多尺度时间序列分解和重构")
    print("  ✓ 基于PCF-GAN的距离度量")
    
    return gan_model

if __name__ == "__main__":
    model = demo_gan_training()
