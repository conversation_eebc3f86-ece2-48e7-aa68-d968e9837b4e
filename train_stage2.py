import os
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
os.environ['TF_XLA_FLAGS'] = '--tf_xla_enable_xla_devices'
os.environ['TF_CPP_MIN_LOG_LEVEL']='2'

import random
import warnings
import numpy as np
import torch
import torch.nn as nn
from torch import optim
from typing import List, Tuple
import matplotlib.pyplot as plt
import shutil
import math
from functools import partial

try:
    from data_loader import load_data
    from metrics.visualization_metrics import visualization
    from pcf_gan import char_func_path, TrendGenerator, SeasonGenerator
    from utils import toggle_grad
except ImportError as e:
    print(f"Import Error: {e}. Please ensure data_loader.py and metrics/visualization_metrics.py are accessible.")
    exit()

class series_decomp(nn.Module):
    def __init__(self, kernel_size):
        super(series_decomp, self).__init__()
        self.moving_avg = nn.AvgPool1d(kernel_size=kernel_size, stride=1, padding=(kernel_size - 1) // 2)
    def forward(self, x):
        moving_mean = self.moving_avg(x.permute(0, 2, 1)).permute(0, 2, 1)
        res = x - moving_mean
        return res, moving_mean

class DataEmbedding_wo_pos(nn.Module):
    def __init__(self, c_in, d_model, dropout=0.1):
        super(DataEmbedding_wo_pos, self).__init__()
        self.value_embedding = nn.Linear(c_in, d_model)
        self.dropout = nn.Dropout(p=dropout)
    def forward(self, x, x_mark):
        x = self.value_embedding(x)
        return self.dropout(x)

class Normalize(nn.Module):
    def __init__(self, num_features: int, eps=1e-5, affine=True):
        super(Normalize, self).__init__()
        self.eps = eps
        self.affine = affine
        if self.affine:
            self.gamma = nn.Parameter(torch.ones(1, 1, num_features))
            self.beta = nn.Parameter(torch.zeros(1, 1, num_features))
    def forward(self, x, mode: str):
        if mode == 'norm':
            self._get_statistics(x)
            x = self._normalize(x)
            return x, self.mean, self.stdev
        elif mode == 'denorm':
            x = self._denormalize(x)
            return x
        else: raise NotImplementedError
    def _get_statistics(self, x):
        self.mean = torch.mean(x, dim=1, keepdim=True).detach()
        self.stdev = torch.sqrt(torch.var(x, dim=1, keepdim=True, unbiased=False) + self.eps).detach()
    def _normalize(self, x):
        x = (x - self.mean) / self.stdev
        if self.affine:
            x = x * self.gamma + self.beta
        return x
    def _denormalize(self, x):
        if not hasattr(self, 'mean') or not hasattr(self, 'stdev'):
             raise RuntimeError("Normalization stats not found. Call in 'norm' mode first.")
        if self.affine:
            x = (x - self.beta) / (self.gamma + self.eps * self.eps)
        x = x * self.stdev + self.mean
        return x

class Config:
    file_path = 'data/stock_data.csv'
    stage1_epochs = 200
    stage2_epochs = 300  # GAN training epochs
    seq_len = 24

    batch_size = 128
    stage1_lr = 0.0001
    stage2_lr_g = 0.0001  # Generator learning rate
    stage2_lr_d = 0.0001  # Discriminator learning rate
    display_epoch_interval = 10
    moving_avg = 9
    focal_loss_gamma = 2.0

    # GAN specific parameters
    noise_dim = 100
    hidden_dim = 128
    add_time = True
    M_num_samples = 64
    M_hidden_dim = 32
    Lambda1 = 1.0  # reconstruction loss weight
    Lambda2 = 0.1  # regularization loss weight
    Lambda3 = 1.0  # adversarial loss weight

    timemixer_params = {
        'd_model': 768,
        'd_ff': 3072,
        'dropout': 0.1,
        'down_sampling_window': 2,
        'down_sampling_layers': 3,
    }
    
    def update_dims(self, num_features):
        self.enc_in = num_features
        self.c_out = num_features
        self.timemixer_params.update({
            'enc_in': num_features, 
            'c_out': num_features,
            'seq_len': self.seq_len,
            'moving_avg': self.moving_avg
        })

cfg = Config()

class DecompositionalEncoder(nn.Module):
    def __init__(self, config: Config):
        super(DecompositionalEncoder, self).__init__()
        tm_params = config.timemixer_params
        self.down_sampling_window = tm_params['down_sampling_window']
        self.down_sampling_layers = tm_params['down_sampling_layers']
        self.enc_in = tm_params['enc_in']
        self.down_pool = nn.AvgPool1d(kernel_size=self.down_sampling_window, stride=self.down_sampling_window)
        self.normalize_layers = nn.ModuleList([Normalize(self.enc_in, affine=True) for _ in range(self.down_sampling_layers + 1)])
        
        self.enc_embedding = DataEmbedding_wo_pos(
            tm_params['enc_in'], tm_params['d_model'], tm_params['dropout']
        )
        
        self.decomposition = series_decomp(tm_params['moving_avg'])

    def forward(self, x_enc: torch.Tensor) -> Tuple[List[torch.Tensor], List[torch.Tensor], torch.Tensor, torch.Tensor]:
        x_enc_transposed = x_enc.permute(0, 2, 1)
        multi_scale_inputs = [x_enc]
        current_x = x_enc_transposed
        for _ in range(self.down_sampling_layers):
            current_x = self.down_pool(current_x)
            multi_scale_inputs.append(current_x.permute(0, 2, 1))
        season_list, trend_list = [], []
        main_mean, main_stdev = None, None
        for i, x in enumerate(multi_scale_inputs):
            x_norm, mean, stdev = self.normalize_layers[i](x, 'norm')
            if i == 0: main_mean, main_stdev = mean, stdev
            embedded_x = self.enc_embedding(x_norm, None)
            season, trend = self.decomposition(embedded_x)
            season_list.append(season)
            trend_list.append(trend)
        return season_list, trend_list, main_mean, main_stdev

class ReconstructionDecoder(nn.Module):
    def __init__(self, config: Config):
        super(ReconstructionDecoder, self).__init__()
        tm_params = config.timemixer_params
        self.season_mixer = MultiScaleSeasonMixing(config)
        self.trend_mixer = MultiScaleTrendMixing(config)
        self.projection_layer = nn.Linear(tm_params['d_model'], tm_params['c_out'])

    def forward(self, season_list: List[torch.Tensor], trend_list: List[torch.Tensor]) -> torch.Tensor:
        season_list_permuted = [s.permute(0, 2, 1) for s in season_list]
        trend_list_permuted = [t.permute(0, 2, 1) for t in trend_list]
        mixed_seasons_permuted = self.season_mixer(season_list_permuted)
        mixed_trends_permuted = self.trend_mixer(trend_list_permuted)
        combined_permuted = mixed_seasons_permuted[0] + mixed_trends_permuted[0]
        combined = combined_permuted.permute(0, 2, 1)
        reconstructed_norm = self.projection_layer(combined)
        return reconstructed_norm

class MultiScaleSeasonMixing(nn.Module):
    def __init__(self, config: Config):
        super(MultiScaleSeasonMixing, self).__init__()
        seq_len, dsw, dsl = config.seq_len, config.timemixer_params['down_sampling_window'], config.timemixer_params['down_sampling_layers']
        self.down_sampling_layers = nn.ModuleList([nn.Sequential(nn.Linear(seq_len // (dsw ** i), seq_len // (dsw ** (i + 1))), nn.GELU(), nn.Linear(seq_len // (dsw ** (i + 1)), seq_len // (dsw ** (i + 1))),) for i in range(dsl)])
    def forward(self, season_list_permuted):
        out_high = season_list_permuted[0]; out_low = season_list_permuted[1]; out_season_list = [out_high]
        for i in range(len(season_list_permuted) - 1):
            out_low_res = self.down_sampling_layers[i](out_high); out_low = out_low + out_low_res; out_high = out_low
            if i + 2 <= len(season_list_permuted) - 1: out_low = season_list_permuted[i + 2]
            out_season_list.append(out_high)
        return out_season_list

class MultiScaleTrendMixing(nn.Module):
    def __init__(self, config: Config):
        super(MultiScaleTrendMixing, self).__init__()
        seq_len, dsw, dsl = config.seq_len, config.timemixer_params['down_sampling_window'], config.timemixer_params['down_sampling_layers']
        self.up_sampling_layers = nn.ModuleList([nn.Sequential(nn.Linear(seq_len // (dsw ** (i + 1)), seq_len // (dsw ** i)), nn.GELU(), nn.Linear(seq_len // (dsw ** i), seq_len // (dsw ** i)),) for i in reversed(range(dsl))])
    def forward(self, trend_list_permuted):
        trend_list_reverse = trend_list_permuted.copy(); trend_list_reverse.reverse(); out_low = trend_list_reverse[0]; out_high = trend_list_reverse[1]; out_trend_list = [out_low]
        for i in range(len(trend_list_reverse) - 1):
            out_high_res = self.up_sampling_layers[i](out_low); out_high = out_high + out_high_res; out_low = out_high
            if i + 2 <= len(trend_list_reverse) - 1: out_high = trend_list_reverse[i + 2]
            out_trend_list.append(out_low)
        out_trend_list.reverse(); return out_trend_list

class TimeMixerGAN(nn.Module):
    """完整的TimeMixer GAN模型"""
    def __init__(self, config: Config):
        super(TimeMixerGAN, self).__init__()
        self.config = config

        # 编码器和解码器（已有的）
        self.encoder = DecompositionalEncoder(config)
        self.decoder = ReconstructionDecoder(config)
        self.denormalizer = Normalize(config.enc_in, affine=True)

        # 趋势和季节生成器
        self.trend_generator = TrendGenerator(
            noise_dim=config.noise_dim,
            seq_len=config.seq_len,
            feature_dim=config.timemixer_params['d_model'],
            hidden_dim=config.hidden_dim
        )
        self.season_generator = SeasonGenerator(
            noise_dim=config.noise_dim,
            seq_len=config.seq_len,
            feature_dim=config.timemixer_params['d_model'],
            hidden_dim=config.hidden_dim
        )

        # 三个鉴别器
        self.season_discriminator = char_func_path(
            num_samples=config.M_num_samples,
            hidden_size=config.M_hidden_dim,
            input_size=config.timemixer_params['d_model'],
            add_time=config.add_time
        )
        self.trend_discriminator = char_func_path(
            num_samples=config.M_num_samples,
            hidden_size=config.M_hidden_dim,
            input_size=config.timemixer_params['d_model'],
            add_time=config.add_time
        )
        self.reconstruction_discriminator = char_func_path(
            num_samples=config.M_num_samples,
            hidden_size=config.M_hidden_dim,
            input_size=config.enc_in,
            add_time=config.add_time
        )

    def generate_fake_sequences(self, batch_size, device):
        """生成虚假的趋势和季节序列"""
        # 生成噪声
        trend_noise = torch.randn(batch_size, self.config.noise_dim).to(device)
        season_noise = torch.randn(batch_size, self.config.noise_dim).to(device)

        # 生成趋势和季节序列
        fake_trends = self.trend_generator(trend_noise)
        fake_seasons = self.season_generator(season_noise)

        # 创建多尺度列表（模拟编码器的输出结构）
        fake_trend_list = [fake_trends]
        fake_season_list = [fake_seasons]

        # 添加下采样版本以匹配编码器输出
        current_trend = fake_trends.permute(0, 2, 1)  # (B, D, T)
        current_season = fake_seasons.permute(0, 2, 1)  # (B, D, T)

        for _ in range(self.config.timemixer_params['down_sampling_layers']):
            current_trend = self.encoder.down_pool(current_trend)
            current_season = self.encoder.down_pool(current_season)
            fake_trend_list.append(current_trend.permute(0, 2, 1))  # (B, T, D)
            fake_season_list.append(current_season.permute(0, 2, 1))  # (B, T, D)

        return fake_trend_list, fake_season_list

    def reconstruct_from_components(self, trend_list, season_list):
        """从趋势和季节分量重构时间序列"""
        return self.decoder(season_list, trend_list)

# --- 4. Visualization and Training Functions ---
def plot_loss_curve(loss_history: List[float], outputs_dir: str):
    plt.figure(figsize=(10, 5)); plt.plot(loss_history, label='Training Loss'); plt.title('Training Loss Curve'); plt.xlabel('Epoch'); plt.ylabel('Loss'); plt.legend(); plt.grid(True); save_path = os.path.join(outputs_dir, "loss_curve.png"); plt.savefig(save_path); plt.close(); print(f"Loss curve saved to {save_path}")
def plot_feature_reconstruction(ori_data: np.ndarray, recon_data: np.ndarray, num_features: int, outputs_dir: str):
    sample_idx = np.random.randint(0, ori_data.shape[0]); original_sample = ori_data[sample_idx]; recon_sample = recon_data[sample_idx]; fig, axes = plt.subplots(nrows=num_features, ncols=1, figsize=(12, 2.5 * num_features), sharex=True)
    if num_features == 1: axes = [axes]
    for i in range(num_features):
        ax = axes[i]; ax.plot(original_sample[:, i], label='Original', color='blue', alpha=0.8); ax.plot(recon_sample[:, i], label='Reconstructed', color='red', linestyle='--', alpha=0.8); ax.set_title(f'Feature {i+1} Comparison'); ax.legend(); ax.grid(True, linestyle='--', alpha=0.5)
    plt.suptitle(f'Feature-wise Reconstruction (Sample #{sample_idx})', fontsize=16); plt.xlabel('Time Step'); plt.tight_layout(rect=[0, 0, 1, 0.96]); save_path = os.path.join(outputs_dir, "feature_reconstruction.png"); plt.savefig(save_path); plt.close(); print(f"Feature reconstruction plot saved to {save_path}")

def stage1_pretrain_autoencoder(config: Config, train_loader,test_loader, device):
    processor = DecompositionalEncoder(config).to(device)
    autoencoder = ReconstructionDecoder(config).to(device)
    denormalizer = Normalize(config.enc_in, affine=True).to(device)

    optimizer = optim.Adam(
        list(processor.parameters()) + list(autoencoder.parameters()),
        lr=config.stage1_lr
    )
    
    criterion_none = nn.L1Loss(reduction='none')
    
    print(f"--- Starting Stage 1: Pre-training with Focal Loss (gamma={config.focal_loss_gamma}) & Increased Capacity ---")
    loss_history = []
    
    for epoch in range(config.stage1_epochs):
        processor.train()
        autoencoder.train()
        total_loss = 0

        for i, (batch_x) in enumerate(train_loader):
            batch_x = batch_x.float().to(device)
            optimizer.zero_grad()
            
            season_list, trend_list, mean, stdev = processor(batch_x)
            reconstructed_norm = autoencoder(season_list, trend_list)
            
            denormalizer.mean = mean
            denormalizer.stdev = stdev
            reconstructed_output = denormalizer(reconstructed_norm, 'denorm')

            sample_wise_error = criterion_none(reconstructed_output, batch_x).mean(dim=[1, 2])
            weights = torch.pow(sample_wise_error, config.focal_loss_gamma).detach()
            loss = (weights * sample_wise_error).mean()
            
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
            
        avg_loss = total_loss / len(train_loader)
        loss_history.append(avg_loss)
        if (epoch + 1) % config.display_epoch_interval == 0:
            print(f"[Stage 1, Epoch {epoch+1}/{config.stage1_epochs}] Reconstruction Loss (Focal): {avg_loss:.6f}")

    print("\n--- Generating plots for Stage 1... ---")
    processor.eval()
    autoencoder.eval()
    with torch.no_grad():
        for batch_x in test_loader:
            batch_x_cpu = batch_x.float()
            batch_x_gpu = batch_x_cpu.to(device)
            season_list, trend_list, mean, stdev = processor(batch_x_gpu)
            reconstructed_norm = autoencoder(season_list, trend_list)
            denormalizer.mean = mean
            denormalizer.stdev = stdev
            reconstructed_output = denormalizer(reconstructed_norm, 'denorm')
    
    ori_data_full = batch_x_cpu.numpy()
    recon_data_full = reconstructed_output.cpu().numpy()

    visualization(ori_data=ori_data_full, generated_data=recon_data_full, analysis="tsne", outputs_dir="gan_outputs", file_name="stage1_reconstruction_focal")
    visualization(ori_data=ori_data_full, generated_data=recon_data_full, analysis="pca",  outputs_dir="gan_outputs", file_name="stage1_reconstruction_focal")
    plot_loss_curve(loss_history, outputs_dir="gan_outputs")
    plot_feature_reconstruction(ori_data_full, recon_data_full, num_features=config.enc_in, outputs_dir="gan_outputs")
    
    torch.save(processor.state_dict(), 'pretrained_models/processor_focal.pth')
    torch.save(autoencoder.state_dict(), 'pretrained_models/reconstruction_decoder_focal.pth')
    print("--- Stage 1 complete. Models trained with focal loss saved. ---")

def stage2_train_gan(config: Config, train_loader, test_loader, device):
    """Stage 2: GAN训练"""
    print(f"--- Starting Stage 2: GAN Training ---")

    # 创建GAN模型
    gan_model = TimeMixerGAN(config).to(device)

    # 加载预训练的编码器和解码器
    gan_model.encoder.load_state_dict(torch.load('pretrained_models/processor_focal.pth'))
    gan_model.decoder.load_state_dict(torch.load('pretrained_models/reconstruction_decoder_focal.pth'))

    # 冻结编码器和解码器参数
    for param in gan_model.encoder.parameters():
        param.requires_grad = False
    for param in gan_model.decoder.parameters():
        param.requires_grad = False

    # 优化器
    g_optimizer = optim.Adam(
        list(gan_model.trend_generator.parameters()) + list(gan_model.season_generator.parameters()),
        lr=config.stage2_lr_g
    )

    d_optimizer = optim.Adam(
        list(gan_model.season_discriminator.parameters()) +
        list(gan_model.trend_discriminator.parameters()) +
        list(gan_model.reconstruction_discriminator.parameters()),
        lr=config.stage2_lr_d
    )

    loss_history = {'g_loss': [], 'd_loss': [], 'season_loss': [], 'trend_loss': [], 'recon_loss': []}

    for epoch in range(config.stage2_epochs):
        gan_model.train()
        total_g_loss = 0
        total_d_loss = 0
        total_season_loss = 0
        total_trend_loss = 0
        total_recon_loss = 0

        for i, (batch_x) in enumerate(train_loader):
            batch_x = batch_x.float().to(device)
            batch_size = batch_x.shape[0]

            # 编码真实数据
            with torch.no_grad():
                real_season_list, real_trend_list, mean, stdev = gan_model.encoder(batch_x)
                gan_model.denormalizer.mean = mean
                gan_model.denormalizer.stdev = stdev

            # ===== 训练鉴别器 =====
            toggle_grad(gan_model.season_discriminator, True)
            toggle_grad(gan_model.trend_discriminator, True)
            toggle_grad(gan_model.reconstruction_discriminator, True)
            toggle_grad(gan_model.trend_generator, False)
            toggle_grad(gan_model.season_generator, False)

            d_optimizer.zero_grad()

            fake_trend_list, fake_season_list = gan_model.generate_fake_sequences(batch_size, device)

            fake_season_mixed = gan_model.decoder.season_mixer([s.permute(0, 2, 1) for s in fake_season_list])
            fake_trend_mixed = gan_model.decoder.trend_mixer([t.permute(0, 2, 1) for t in fake_trend_list])
            real_season_mixed = gan_model.decoder.season_mixer([s.permute(0, 2, 1) for s in real_season_list])
            real_trend_mixed = gan_model.decoder.trend_mixer([t.permute(0, 2, 1) for t in real_trend_list])

            fake_reconstructed_norm = gan_model.reconstruct_from_components(fake_trend_list, fake_season_list)
            fake_reconstructed = gan_model.denormalizer(fake_reconstructed_norm, 'denorm')

            season_d_loss = -gan_model.season_discriminator.distance_measure(
                real_season_mixed[0].permute(0, 2, 1), fake_season_mixed[0].permute(0, 2, 1)
            )

            trend_d_loss = -gan_model.trend_discriminator.distance_measure(
                real_trend_mixed[0].permute(0, 2, 1), fake_trend_mixed[0].permute(0, 2, 1)
            )
            recon_d_loss = -gan_model.reconstruction_discriminator.distance_measure(
                batch_x, fake_reconstructed
            )

            d_loss = season_d_loss + trend_d_loss + recon_d_loss
            d_loss.backward()
            d_optimizer.step()

            # ===== 训练生成器 =====
            toggle_grad(gan_model.season_discriminator, False)
            toggle_grad(gan_model.trend_discriminator, False)
            toggle_grad(gan_model.reconstruction_discriminator, False)
            toggle_grad(gan_model.trend_generator, True)
            toggle_grad(gan_model.season_generator, True)

            g_optimizer.zero_grad()

            fake_trend_list, fake_season_list = gan_model.generate_fake_sequences(batch_size, device)

            fake_season_mixed = gan_model.decoder.season_mixer([s.permute(0, 2, 1) for s in fake_season_list])
            fake_trend_mixed = gan_model.decoder.trend_mixer([t.permute(0, 2, 1) for t in fake_trend_list])
            fake_reconstructed_norm = gan_model.reconstruct_from_components(fake_trend_list, fake_season_list)
            fake_reconstructed = gan_model.denormalizer(fake_reconstructed_norm, 'denorm')

            season_g_loss = gan_model.season_discriminator.distance_measure(
                real_season_mixed[0].permute(0, 2, 1), fake_season_mixed[0].permute(0, 2, 1)
            )
            trend_g_loss = gan_model.trend_discriminator.distance_measure(
                real_trend_mixed[0].permute(0, 2, 1), fake_trend_mixed[0].permute(0, 2, 1)
            )
            recon_g_loss = gan_model.reconstruction_discriminator.distance_measure(
                batch_x, fake_reconstructed
            )

            g_loss = config.Lambda1 * season_g_loss + config.Lambda2 * trend_g_loss + config.Lambda3 * recon_g_loss
            g_loss.backward()
            g_optimizer.step()

            total_g_loss += g_loss.item()
            total_d_loss += d_loss.item()
            total_season_loss += season_g_loss.item()
            total_trend_loss += trend_g_loss.item()
            total_recon_loss += recon_g_loss.item()

        avg_g_loss = total_g_loss / len(train_loader)
        avg_d_loss = total_d_loss / len(train_loader)
        avg_season_loss = total_season_loss / len(train_loader)
        avg_trend_loss = total_trend_loss / len(train_loader)
        avg_recon_loss = total_recon_loss / len(train_loader)

        loss_history['g_loss'].append(avg_g_loss)
        loss_history['d_loss'].append(avg_d_loss)
        loss_history['season_loss'].append(avg_season_loss)
        loss_history['trend_loss'].append(avg_trend_loss)
        loss_history['recon_loss'].append(avg_recon_loss)

        if (epoch + 1) % config.display_epoch_interval == 0:
            print(f"[Stage 2, Epoch {epoch+1}/{config.stage2_epochs}] "
                  f"G_Loss: {avg_g_loss:.6f}, D_Loss: {avg_d_loss:.6f}, "
                  f"Season: {avg_season_loss:.6f}, Trend: {avg_trend_loss:.6f}, Recon: {avg_recon_loss:.6f}")

    torch.save(gan_model.state_dict(), 'pretrained_models/timemixer_gan.pth')
    print("--- Stage 2 complete. GAN model saved. ---")

    return loss_history

if __name__ == '__main__':
    random.seed(8760); np.random.seed(8760); torch.manual_seed(8760); torch.cuda.manual_seed(8760)
    warnings.filterwarnings('ignore'); device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"--- Running on Device: {device} ---")
    
    print(f"Loading data from: {cfg.file_path}")
    train_loader, num_features = load_data(
        file_path=cfg.file_path, seq_len=cfg.seq_len, batch_size=cfg.batch_size
    )
    test_loader, num_features = load_data(
        file_path=cfg.file_path, seq_len=cfg.seq_len, batch_size=3662
    )
    
    cfg.update_dims(num_features)
    print(f"Data loaded. Number of features: {num_features}")
    print(f"Model capacity: d_model={cfg.timemixer_params['d_model']}, d_ff={cfg.timemixer_params['d_ff']}")
    print(f"Training for {cfg.stage1_epochs} epochs with focal loss (gamma={cfg.focal_loss_gamma})")

    # print("Clearing directories for a fresh run...")
    # for dir_path in ['gan_outputs', 'pretrained_models']:
    #     if os.path.exists(dir_path): shutil.rmtree(dir_path)
    #     os.makedirs(dir_path)

    # # Stage 1: 预训练编码器和解码器
    # stage1_pretrain_autoencoder(cfg, train_loader, test_loader, device)

    # Stage 2: GAN训练
    print(f"\n--- Starting Stage 2: GAN Training for {cfg.stage2_epochs} epochs ---")
    gan_loss_history = stage2_train_gan(cfg, train_loader, test_loader, device)

    # 绘制GAN训练损失曲线
    plt.figure(figsize=(15, 10))

    plt.subplot(2, 3, 1)
    plt.plot(gan_loss_history['g_loss'], label='Generator Loss')
    plt.title('Generator Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)

    plt.subplot(2, 3, 2)
    plt.plot(gan_loss_history['d_loss'], label='Discriminator Loss')
    plt.title('Discriminator Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)

    plt.subplot(2, 3, 3)
    plt.plot(gan_loss_history['season_loss'], label='Season Loss')
    plt.title('Season Component Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)

    plt.subplot(2, 3, 4)
    plt.plot(gan_loss_history['trend_loss'], label='Trend Loss')
    plt.title('Trend Component Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)

    plt.subplot(2, 3, 5)
    plt.plot(gan_loss_history['recon_loss'], label='Reconstruction Loss')
    plt.title('Reconstruction Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)

    plt.tight_layout()
    plt.savefig(os.path.join('gan_outputs', 'gan_training_losses.png'))
    plt.close()
    print("GAN training loss curves saved to gan_outputs/gan_training_losses.png")

    print("--- All training stages completed successfully! ---")