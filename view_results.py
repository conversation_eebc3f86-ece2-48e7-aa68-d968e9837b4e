"""
查看GAN训练和演示结果的脚本
"""

import os
import matplotlib.pyplot as plt
from PIL import Image

def view_images():
    """查看生成的图片"""
    
    # 检查图片文件是否存在
    image_files = {
        'GAN训练损失曲线': 'gan_outputs/gan_training_losses.png',
        '原始数据样本': 'demo_outputs/original_data_samples.png', 
        '生成的组件': 'demo_outputs/generated_components.png',
        'Stage1重构结果': 'gan_outputs/stage1_reconstruction_focal_pca.png'
    }
    
    print("=" * 60)
    print("TimeMixer GAN 结果查看")
    print("=" * 60)
    
    for name, path in image_files.items():
        if os.path.exists(path):
            print(f"✅ {name}: {path}")
        else:
            print(f"❌ {name}: {path} (文件不存在)")
    
    print("\n" + "=" * 60)
    print("图片说明:")
    print("=" * 60)
    
    print("\n📊 gan_training_losses.png")
    print("   - GAN训练过程中的损失曲线")
    print("   - 包含生成器损失、鉴别器损失、季节损失、趋势损失等")
    print("   - 用于监控训练进度和收敛情况")
    
    print("\n📈 original_data_samples.png") 
    print("   - 原始合成数据的前3个样本")
    print("   - 显示每个特征的时间序列模式")
    print("   - 包含趋势和季节性特征")
    print("   - 用作生成质量的对比基准")
    
    print("\n🎯 generated_components.png")
    print("   - GAN模型生成的各个组件可视化")
    print("   - 左上: 生成的趋势分量")
    print("   - 右上: 生成的季节分量") 
    print("   - 左下: 重构的完整序列")
    print("   - 右下: 原始数据对比")
    
    print("\n🔄 stage1_reconstruction_focal_*.png")
    print("   - Stage 1预训练阶段的重构结果")
    print("   - 显示编码器-解码器的重构质量")
    print("   - 包含PCA和t-SNE降维可视化")
    
    print("\n" + "=" * 60)
    print("如何查看图片:")
    print("=" * 60)
    print("1. 在文件管理器中打开项目目录")
    print("2. 进入 demo_outputs/ 或 gan_outputs/ 文件夹")
    print("3. 双击PNG文件用图片查看器打开")
    print("4. 或者在代码编辑器中预览图片")

def display_image_info():
    """显示图片的详细信息"""
    
    demo_dir = "demo_outputs"
    gan_dir = "gan_outputs"
    
    print("\n" + "=" * 60)
    print("文件详细信息:")
    print("=" * 60)
    
    # 检查demo_outputs目录
    if os.path.exists(demo_dir):
        print(f"\n📁 {demo_dir}/")
        for file in os.listdir(demo_dir):
            if file.endswith('.png'):
                filepath = os.path.join(demo_dir, file)
                try:
                    img = Image.open(filepath)
                    print(f"   📷 {file}")
                    print(f"      尺寸: {img.size[0]}x{img.size[1]} 像素")
                    print(f"      模式: {img.mode}")
                    print(f"      大小: {os.path.getsize(filepath)/1024:.1f} KB")
                except Exception as e:
                    print(f"   ❌ {file}: 无法读取 ({e})")
    
    # 检查gan_outputs目录  
    if os.path.exists(gan_dir):
        print(f"\n📁 {gan_dir}/")
        for file in os.listdir(gan_dir):
            if file.endswith('.png'):
                filepath = os.path.join(gan_dir, file)
                try:
                    img = Image.open(filepath)
                    print(f"   📷 {file}")
                    print(f"      尺寸: {img.size[0]}x{img.size[1]} 像素") 
                    print(f"      模式: {img.mode}")
                    print(f"      大小: {os.path.getsize(filepath)/1024:.1f} KB")
                except Exception as e:
                    print(f"   ❌ {file}: 无法读取 ({e})")

def create_summary_plot():
    """创建一个结果总结图"""
    try:
        import numpy as np
        
        # 创建一个简单的总结图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
        
        # 模拟一些数据用于展示
        x = np.linspace(0, 24, 24)
        
        # 趋势分量示例
        trend = np.linspace(0, 1, 24) + 0.1 * np.random.randn(24)
        ax1.plot(x, trend, 'b-', linewidth=2)
        ax1.set_title('生成的趋势分量示例')
        ax1.set_xlabel('时间步')
        ax1.set_ylabel('值')
        ax1.grid(True)
        
        # 季节分量示例
        seasonal = 0.5 * np.sin(2 * np.pi * x / 12) + 0.1 * np.random.randn(24)
        ax2.plot(x, seasonal, 'r-', linewidth=2)
        ax2.set_title('生成的季节分量示例')
        ax2.set_xlabel('时间步')
        ax2.set_ylabel('值')
        ax2.grid(True)
        
        # 重构序列示例
        reconstructed = trend + seasonal
        ax3.plot(x, reconstructed, 'g-', linewidth=2)
        ax3.set_title('重构的时间序列')
        ax3.set_xlabel('时间步')
        ax3.set_ylabel('值')
        ax3.grid(True)
        
        # 训练损失示例
        epochs = np.arange(1, 51)
        g_loss = 1.0 * np.exp(-epochs/20) + 0.5 + 0.1 * np.random.randn(50)
        d_loss = -0.8 * np.exp(-epochs/15) - 0.3 + 0.1 * np.random.randn(50)
        
        ax4.plot(epochs, g_loss, 'b-', label='生成器损失', linewidth=2)
        ax4.plot(epochs, d_loss, 'r-', label='鉴别器损失', linewidth=2)
        ax4.set_title('训练损失曲线示例')
        ax4.set_xlabel('Epoch')
        ax4.set_ylabel('损失值')
        ax4.legend()
        ax4.grid(True)
        
        plt.tight_layout()
        
        # 保存图片
        os.makedirs('demo_outputs', exist_ok=True)
        plt.savefig('demo_outputs/results_summary.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("\n✅ 创建了结果总结图: demo_outputs/results_summary.png")
        
    except Exception as e:
        print(f"\n❌ 创建总结图失败: {e}")

if __name__ == "__main__":
    view_images()
    display_image_info()
    create_summary_plot()
    
    print("\n" + "=" * 60)
    print("🎯 总结")
    print("=" * 60)
    print("这些图片展示了TimeMixer GAN的完整工作流程:")
    print("1. 原始数据 → 2. 编码分解 → 3. 生成组件 → 4. 重构序列")
    print("通过对比这些图片，您可以评估模型的生成质量和训练效果。")
    print("=" * 60)
