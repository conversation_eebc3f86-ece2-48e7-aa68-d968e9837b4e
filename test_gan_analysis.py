"""
测试GAN分析功能的脚本
"""

import torch
import numpy as np
from train_stage2 import Config, TimeMixerGAN, test_gan_model, perform_gan_analysis
import os

def test_gan_analysis_functions():
    """测试GAN分析功能"""
    print("=" * 60)
    print("测试GAN分析功能")
    print("=" * 60)
    
    # 创建配置
    config = Config()
    config.update_dims(3)  # 3个特征
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 创建输出目录
    os.makedirs('test_outputs', exist_ok=True)
    
    try:
        # 创建GAN模型
        print("\n1. 创建GAN模型...")
        gan_model = TimeMixerGAN(config).to(device)
        
        # 创建测试数据
        print("2. 创建测试数据...")
        batch_size = 16
        seq_len = config.seq_len
        num_features = config.enc_in
        
        # 模拟真实数据和生成数据
        real_data = np.random.randn(batch_size, seq_len, num_features)
        generated_data = np.random.randn(batch_size, seq_len, num_features) * 0.8 + 0.2
        
        print(f"真实数据形状: {real_data.shape}")
        print(f"生成数据形状: {generated_data.shape}")
        
        # 测试PCA和t-SNE分析
        print("\n3. 测试PCA和t-SNE分析...")
        perform_gan_analysis(real_data, generated_data, epoch=50, outputs_dir='test_outputs')
        
        # 检查生成的文件
        print("\n4. 检查生成的文件...")
        expected_files = [
            'test_outputs/gan_pca_epoch_50.png',
            'test_outputs/gan_tsne_epoch_50.png'
        ]
        
        for file_path in expected_files:
            if os.path.exists(file_path):
                print(f"✅ {file_path} 已生成")
            else:
                print(f"❌ {file_path} 未找到")
        
        # 测试完整的GAN测试流程
        print("\n5. 测试完整的GAN测试流程...")
        
        # 创建简单的数据加载器
        class SimpleTestLoader:
            def __init__(self, data, batch_size):
                self.data = torch.FloatTensor(data)
                self.batch_size = batch_size
                self.num_batches = len(data) // batch_size
            
            def __iter__(self):
                for i in range(0, len(self.data), self.batch_size):
                    batch = self.data[i:i+self.batch_size]
                    yield (batch,)
            
            def __len__(self):
                return self.num_batches
        
        # 创建测试数据加载器
        test_data = np.random.randn(64, seq_len, num_features)
        test_loader = SimpleTestLoader(test_data, batch_size=8)
        
        # 运行GAN测试
        try:
            test_gan_model(gan_model, test_loader, device, epoch=100, outputs_dir='test_outputs')
            print("✅ GAN测试流程运行成功")
        except Exception as e:
            print(f"⚠ GAN测试流程遇到问题: {e}")
        
        print("\n6. 最终文件检查...")
        test_files = [
            'test_outputs/gan_pca_epoch_100.png',
            'test_outputs/gan_tsne_epoch_100.png'
        ]
        
        for file_path in test_files:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path) / 1024  # KB
                print(f"✅ {file_path} 已生成 ({file_size:.1f} KB)")
            else:
                print(f"❌ {file_path} 未找到")
        
        print("\n" + "=" * 60)
        print("🎉 GAN分析功能测试完成！")
        print("=" * 60)
        print("功能说明:")
        print("- 每50个epoch自动进行PCA和t-SNE分析")
        print("- 对比真实数据和生成数据的分布")
        print("- 图片保存到gan_outputs文件夹")
        print("- 文件名包含epoch信息")
        print("- 所有图片仅保存，不显示")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def demonstrate_epoch_testing():
    """演示epoch测试功能"""
    print("\n" + "=" * 60)
    print("演示epoch测试功能")
    print("=" * 60)
    
    print("在实际训练中，以下epoch会进行测试:")
    epochs = [50, 100, 150, 200, 250, 300]
    
    for epoch in epochs:
        print(f"  Epoch {epoch}: 生成 gan_pca_epoch_{epoch}.png 和 gan_tsne_epoch_{epoch}.png")
    
    print("\n这些图片将帮助您:")
    print("1. 监控生成质量的变化")
    print("2. 观察训练过程中的改进")
    print("3. 识别过拟合或欠拟合")
    print("4. 比较不同epoch的生成效果")

if __name__ == "__main__":
    success = test_gan_analysis_functions()
    demonstrate_epoch_testing()
    
    if success:
        print("\n🎯 所有测试通过！GAN分析功能已准备就绪。")
    else:
        print("\n⚠ 部分测试失败，请检查实现。")
