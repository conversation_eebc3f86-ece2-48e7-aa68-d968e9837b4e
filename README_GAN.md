# TimeMixer GAN 实现说明

## 概述

本项目成功将PCF-GAN的生成器和鉴别器移植到TimeMixerGAN+项目中，构建了一个完整的GAN模型，包含：

- **编码器和解码器**：基于原有的DecompositionalEncoder和ReconstructionDecoder
- **双生成器架构**：趋势序列生成器和季节序列生成器
- **三鉴别器架构**：季节分量鉴别器、趋势分量鉴别器和重构序列鉴别器

## 架构设计

### 1. 编码器 (DecompositionalEncoder)
- 对输入时间序列进行多尺度下采样
- 使用序列分解将每个尺度分解为趋势和季节分量
- 输出多尺度的趋势列表和季节列表

### 2. 解码器 (ReconstructionDecoder)
- 使用MultiScaleSeasonMixing和MultiScaleTrendMixing重构序列
- 将多尺度的趋势和季节分量融合为最终的时间序列

### 3. 生成器
- **TrendGenerator**: 从随机噪声生成趋势序列
- **SeasonGenerator**: 从随机噪声生成季节序列
- 两个生成器独立训练，分别负责不同的时间序列特征

### 4. 鉴别器
基于PCF-GAN的char_func_path实现，使用路径特征函数计算距离：

- **季节分量鉴别器**: 最小化真实和生成的季节分量分布差异
- **趋势分量鉴别器**: 最小化真实和生成的趋势分量分布差异  
- **重构序列鉴别器**: 最大化真实和生成的完整时间序列差异

## 核心文件

### 新增文件
- `pcf_gan.py`: PCF-GAN核心组件，包含char_func_path和生成器
- `pcf_nn.py`: 神经网络层实现，包含development_layer和projection
- `unitary.py`: 酉群相关的数学运算
- `utils.py`: 工具函数
- `test_gan.py`: 组件测试脚本
- `demo_gan.py`: 演示脚本

### 修改文件
- `train_stage2.py`: 集成GAN训练逻辑

## 训练流程

### Stage 1: 预训练编码器和解码器
```python
stage1_pretrain_autoencoder(cfg, train_loader, test_loader, device)
```

### Stage 2: GAN训练
```python
gan_loss_history = stage2_train_gan(cfg, train_loader, test_loader, device)
```

训练过程包括：
1. **鉴别器训练**: 最大化真实数据和生成数据的距离
2. **生成器训练**: 最小化真实数据和生成数据的距离
3. **混合损失函数**: 
   ```
   d_loss = season_d_loss + trend_d_loss + recon_d_loss
   g_loss = λ1 * season_g_loss + λ2 * trend_g_loss + λ3 * recon_g_loss
   ```

## 使用方法

### 1. 运行测试
```bash
python test_gan.py
```

### 2. 运行演示
```bash
python demo_gan.py
```

### 3. 完整训练
```bash
python train_stage2.py
```

## 配置参数

在`Config`类中可以调整的关键参数：

```python
# GAN训练参数
stage2_epochs = 300          # GAN训练轮数
stage2_lr_g = 0.0001        # 生成器学习率
stage2_lr_d = 0.0001        # 鉴别器学习率

# 生成器参数
noise_dim = 100             # 噪声维度
hidden_dim = 128            # 隐藏层维度

# PCF-GAN参数
add_time = True             # 是否添加时间维度
M_num_samples = 64          # 特征函数样本数
M_hidden_dim = 32           # 特征函数隐藏维度

# 损失权重
Lambda1 = 1.0               # 季节分量损失权重
Lambda2 = 0.1               # 趋势分量损失权重  
Lambda3 = 1.0               # 重构损失权重
```

## 模型特点

### ✅ 已实现功能
- [x] PCF-GAN核心模块移植
- [x] 双生成器架构（趋势+季节）
- [x] 三鉴别器架构
- [x] 多尺度时间序列处理
- [x] 完整的训练流程
- [x] 可视化和评估

### 🔧 技术亮点
- **多尺度分解**: 支持不同时间尺度的特征学习
- **组件分离**: 独立建模趋势和季节特征
- **路径特征**: 使用PCF距离度量时间序列相似性
- **端到端训练**: 生成器和鉴别器联合优化

### 📊 模型规模
- 编码器参数: ~3K
- 解码器参数: ~4K  
- 趋势生成器: ~2.4M
- 季节生成器: ~2.4M
- 鉴别器总计: ~101M

## 下一步改进

1. **超参数调优**: 优化学习率、损失权重等
2. **数据增强**: 添加更多时间序列变换
3. **评估指标**: 实现更全面的生成质量评估
4. **模型压缩**: 减少鉴别器参数量
5. **条件生成**: 支持条件时间序列生成

## 依赖要求

- PyTorch >= 1.8.0
- NumPy
- Matplotlib
- 其他标准Python库

## 致谢

本实现基于以下工作：
- TimeMixer: 多尺度时间序列分解
- PCF-GAN: 路径特征函数生成对抗网络
